Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit EnterpriseS' Language: 'en' Physical Memory: 12214 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-11T12:55:55Z

COMMAND LINE ARGUMENTS:
D:\Unity\Editors\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/Projects/Packages
-logFile
Logs/AssetImportWorker0.log
-srvPort
8421
-job-worker-count
1
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Unity/Projects/Packages
D:/Unity/Projects/Packages
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23360]  Target information:

Player connection [23360]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 497112505 [EditorId] 497112505 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23360]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 497112505 [EditorId] 497112505 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23360] Host joined multi-casting on [***********:54997]...
Player connection [23360] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 1
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 7.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path D:/Unity/Editors/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/Projects/Packages/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        Intel(R) HD Graphics 620 (ID=0x5916)
    Vendor:          Intel
    VRAM:            6107 MB
    App VRAM Budget: 5546 MB
    Driver:          31.0.101.2130
    Unified Memory Architecture
    Cache Coherent UMA
Initialize mono
Mono path[0] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56024
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.050822 seconds.
- Loaded All Assemblies, in  4.503 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 670 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.370 seconds
Domain Reload Profiling: 6871ms
	BeginReloadAssembly (1144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (3ms)
	RebuildCommonClasses (926ms)
	RebuildNativeTypeToScriptingClass (418ms)
	initialDomainReloadingComplete (1598ms)
	LoadAllAssembliesAndSetupDomain (416ms)
		LoadAssemblies (1142ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (402ms)
			TypeCache.Refresh (399ms)
				TypeCache.ScanAssembly (374ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2370ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1741ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (873ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (421ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.072 seconds
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.542 seconds
Domain Reload Profiling: 4612ms
	BeginReloadAssembly (359ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (1545ms)
		LoadAssemblies (749ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (982ms)
			TypeCache.Refresh (758ms)
				TypeCache.ScanAssembly (700ms)
			BuildScriptInfoCaches (189ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (2543ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2098ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (338ms)
			ProcessInitializeOnLoadAttributes (1457ms)
			ProcessInitializeOnLoadMethodAttributes (268ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 217 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6642 unused Assets / (5.0 MB). Loaded Objects now: 7357.
Memory consumption went from 166.8 MB to 161.7 MB.
Total: 16.907300 ms (FindLiveObjects: 1.234300 ms CreateObjectMapping: 2.148800 ms MarkObjects: 8.576600 ms  DeleteObjects: 4.946000 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.843 seconds
Refreshing native plugins compatible for Editor in 1.60 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.952 seconds
Domain Reload Profiling: 4791ms
	BeginReloadAssembly (508ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (59ms)
	LoadAllAssembliesAndSetupDomain (1169ms)
		LoadAssemblies (865ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (571ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (517ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (2952ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2368ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (411ms)
			ProcessInitializeOnLoadAttributes (1728ms)
			ProcessInitializeOnLoadMethodAttributes (179ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 2.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 23 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6638 unused Assets / (5.1 MB). Loaded Objects now: 7375.
Memory consumption went from 140.9 MB to 135.7 MB.
Total: 21.714400 ms (FindLiveObjects: 1.594000 ms CreateObjectMapping: 1.995700 ms MarkObjects: 9.227800 ms  DeleteObjects: 8.895200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 517678.514058 seconds.
  path: Assets/Tools/Runtime/Monetization/Ads/Common/AdEnums.cs
  artifactKey: Guid(346cf01eb1c76384588e545a227a0077) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Monetization/Ads/Common/AdEnums.cs using Guid(346cf01eb1c76384588e545a227a0077) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c61edfb5d6f0f0ff5691786bbcd7f61d') in 0.0102903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6632 unused Assets / (3.8 MB). Loaded Objects now: 7378.
Memory consumption went from 139.9 MB to 136.1 MB.
Total: 19.754700 ms (FindLiveObjects: 1.587900 ms CreateObjectMapping: 2.309000 ms MarkObjects: 12.329200 ms  DeleteObjects: 3.526600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 139.790864 seconds.
  path: Assets/Tools/Runtime/Monetization/Ads/Common/IAdUnit.cs
  artifactKey: Guid(d0da1f3db990c144db10a6ffc0537c5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Monetization/Ads/Common/IAdUnit.cs using Guid(d0da1f3db990c144db10a6ffc0537c5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '74ba61238433cb002705797a0e0bf9ca') in 0.0014614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.987 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.296 seconds
Domain Reload Profiling: 4286ms
	BeginReloadAssembly (502ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (82ms)
	RebuildNativeTypeToScriptingClass (136ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (1221ms)
		LoadAssemblies (920ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (570ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (517ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (2297ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1738ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (43ms)
			SetLoadedEditorAssemblies (25ms)
			BeforeProcessingInitializeOnLoad (390ms)
			ProcessInitializeOnLoadAttributes (1148ms)
			ProcessInitializeOnLoadMethodAttributes (126ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 1.47 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6639 unused Assets / (4.8 MB). Loaded Objects now: 7383.
Memory consumption went from 139.6 MB to 134.8 MB.
Total: 15.569800 ms (FindLiveObjects: 1.221500 ms CreateObjectMapping: 1.942600 ms MarkObjects: 8.419700 ms  DeleteObjects: 3.984100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6633 unused Assets / (3.9 MB). Loaded Objects now: 7384.
Memory consumption went from 141.9 MB to 138.0 MB.
Total: 16.515300 ms (FindLiveObjects: 1.466500 ms CreateObjectMapping: 2.324700 ms MarkObjects: 8.059500 ms  DeleteObjects: 4.662900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 18.556635 seconds.
  path: Assets/Tools/Runtime/Monetization/Ads/Common/IBannerAd.cs
  artifactKey: Guid(512582858822ede4491a9f2d3a939ad4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Monetization/Ads/Common/IBannerAd.cs using Guid(512582858822ede4491a9f2d3a939ad4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '79106e0ec49592fafb2aabc562394b40') in 0.0051035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.996 seconds
Refreshing native plugins compatible for Editor in 2.35 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.507 seconds
Domain Reload Profiling: 4502ms
	BeginReloadAssembly (551ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (103ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (1258ms)
		LoadAssemblies (1004ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (607ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (553ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (2508ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1855ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (50ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (368ms)
			ProcessInitializeOnLoadAttributes (1274ms)
			ProcessInitializeOnLoadMethodAttributes (146ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 1.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6640 unused Assets / (3.8 MB). Loaded Objects now: 7389.
Memory consumption went from 139.7 MB to 135.8 MB.
Total: 18.932800 ms (FindLiveObjects: 1.378900 ms CreateObjectMapping: 2.489000 ms MarkObjects: 11.228600 ms  DeleteObjects: 3.834500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6634 unused Assets / (3.7 MB). Loaded Objects now: 7390.
Memory consumption went from 141.9 MB to 138.3 MB.
Total: 23.823500 ms (FindLiveObjects: 1.850800 ms CreateObjectMapping: 2.394500 ms MarkObjects: 13.809600 ms  DeleteObjects: 5.766800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 17.196633 seconds.
  path: Assets/Tools/Runtime/Monetization/Ads/Common/IInterstitialAd.cs
  artifactKey: Guid(b1a1096fcee5cc64b8bd71924bc6e8f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Monetization/Ads/Common/IInterstitialAd.cs using Guid(b1a1096fcee5cc64b8bd71924bc6e8f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '74b4249a58d068fd694fd3d09d478255') in 0.0040932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.992 seconds
Refreshing native plugins compatible for Editor in 2.88 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.023 seconds
Domain Reload Profiling: 6022ms
	BeginReloadAssembly (490ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (176ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (1242ms)
		LoadAssemblies (935ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (577ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (528ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (4024ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3417ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (48ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (340ms)
			ProcessInitializeOnLoadAttributes (2828ms)
			ProcessInitializeOnLoadMethodAttributes (185ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6641 unused Assets / (8.8 MB). Loaded Objects now: 7395.
Memory consumption went from 139.7 MB to 130.9 MB.
Total: 39.722900 ms (FindLiveObjects: 1.692100 ms CreateObjectMapping: 1.837800 ms MarkObjects: 20.033900 ms  DeleteObjects: 16.157000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 154.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (2.1 MB). Loaded Objects now: 7396.
Memory consumption went from 141.9 MB to 139.9 MB.
Total: 28.964000 ms (FindLiveObjects: 1.618300 ms CreateObjectMapping: 2.210300 ms MarkObjects: 19.132800 ms  DeleteObjects: 6.000900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.366 seconds
Refreshing native plugins compatible for Editor in 3.87 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.000 seconds
Domain Reload Profiling: 6402ms
	BeginReloadAssembly (2638ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (1106ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (407ms)
	RebuildCommonClasses (114ms)
	RebuildNativeTypeToScriptingClass (110ms)
	initialDomainReloadingComplete (119ms)
	LoadAllAssembliesAndSetupDomain (1420ms)
		LoadAssemblies (1703ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (506ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (428ms)
			ResolveRequiredComponents (44ms)
	FinalizeReload (2001ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1360ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (257ms)
			ProcessInitializeOnLoadAttributes (949ms)
			ProcessInitializeOnLoadMethodAttributes (115ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 1.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6642 unused Assets / (4.2 MB). Loaded Objects now: 7401.
Memory consumption went from 143.8 MB to 139.6 MB.
Total: 15.362500 ms (FindLiveObjects: 1.235000 ms CreateObjectMapping: 2.022800 ms MarkObjects: 7.835500 ms  DeleteObjects: 4.267700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 56.701359 seconds.
  path: Assets/Tools/Runtime/Monetization/Ads/Common/IRewardedAd.cs
  artifactKey: Guid(2d6c8050a21db5846becd38267113c85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Monetization/Ads/Common/IRewardedAd.cs using Guid(2d6c8050a21db5846becd38267113c85) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '77a4f357169a4372e5a3560c665571d7') in 0.0023298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

